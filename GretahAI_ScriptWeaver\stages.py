"""
Stage functions for the GretahAI ScriptWeaver application.
Each stage function handles a specific part of the application flow.
"""

import os
import json
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.stages")

# Import helper functions from other modules
from core.excel_parser import parse_excel
from core.ai import convert_test_case_to_step_table
from core.ai_helpers import analyze_step_table
from core.config import APP_CONFIG_FILE

def load_google_api_key():
    """Load Google API key from config file or environment variable."""
    # Try to load from config file first
    try:
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, "r") as f:
                config = json.load(f)
                if "google_api_key" in config and config["google_api_key"]:
                    return config["google_api_key"]
    except Exception as e:
        logger.error(f"Error loading config file: {e}")

    # Fall back to environment variable
    return os.environ.get("GOOGLE_API_KEY", "")

@st.cache_data
def parse_excel_cached(file_content):
    """
    Cached version of parse_excel function to avoid redundant processing.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    logger.info("Using cached parse_excel function")

    # Create a temporary file to pass to parse_excel
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_file.write(file_content)
        temp_file_path = temp_file.name

    try:
        # Parse the Excel file using the existing function
        test_cases = parse_excel(temp_file_path)
        # Clean up the temporary file
        os.unlink(temp_file_path)
        return test_cases
    except Exception as e:
        # Clean up the temporary file even if parsing fails
        os.unlink(temp_file_path)
        logger.error(f"Error in cached parse_excel: {e}")
        raise e

def stage1_upload_excel(state):
    """Phase 1: Upload Excel File."""
    st.markdown("<h2 class='stage-header'>Phase 1: Upload Test Case Excel</h2>", unsafe_allow_html=True)

    # Help text in an expander to reduce visual clutter
    with st.expander("About Excel Format", expanded=False):
        st.markdown("""
        Upload an Excel file with the following columns:
        - **Test Case ID**: Unique identifier
        - **Test Case Objective**: Description of what is being tested
        - **Step No**: Step number
        - **Test Steps**: Action to perform
        - **Expected Result**: Expected outcome
        """)

    # Simplified file uploader with clearer label
    uploaded_file = st.file_uploader("Select Excel file (.xlsx)", type=["xlsx"], key="excel_uploader")

    if uploaded_file is not None:
        try:
            # Get the file content
            file_content = uploaded_file.getvalue()

            # Check if this is the same file we've already processed
            if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == hash(file_content):
                logger.info("File content unchanged - skipping reprocessing")
            else:
                logger.info("New or changed file detected - processing")
                # Update the content hash in state
                state.last_file_content_hash = hash(file_content)

                # Save the uploaded file to a temporary location
                temp_dir = Path("temp_uploads")
                temp_dir.mkdir(exist_ok=True)

                # Use a consistent filename based on the uploaded file name instead of timestamp
                safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
                temp_file_path = temp_dir / f"test_cases_{safe_filename}"

                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Process file in a collapsible section
                with st.expander("Processing Results", expanded=True):
                    # Verify the file was saved correctly
                    if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
                        state.uploaded_excel = str(temp_file_path)
                        st.success(f"✅ File uploaded: {uploaded_file.name}")

                        # Parse the excel file using the cached function
                        if parse_excel:
                            try:
                                state.test_cases = parse_excel_cached(file_content)
                                if not state.test_cases:
                                    st.warning("⚠️ No test cases found. Check file format.")
                                else:
                                    st.success(f"✅ Parsed {len(state.test_cases)} test cases")
                            except Exception as e:
                                st.error(f"❌ Error parsing file: {e}")
                                state.test_cases = None # Ensure it's reset on error
                        else:
                            st.warning("⚠️ Excel parsing function not available")
                    else:
                        st.error("❌ Failed to save file")

            # Always display a preview of the Excel file (using the cached file if available)
            if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
                try:
                    df = pd.read_excel(state.uploaded_excel) # Read from the saved temp file
                    with st.expander("Preview Test Cases", expanded=True):
                        st.dataframe(df)
                except Exception as e:
                    st.error(f"❌ Error reading file: {e}")
        except Exception as e:
            st.error(f"❌ Error processing file: {e}")

def stage2_enter_website(state):
    """Phase 2: Website Configuration."""
    st.markdown("<h2 class='stage-header'>Phase 2: Website Configuration</h2>", unsafe_allow_html=True)

    # Main configuration section
    with st.container():
        # Get existing URL from state or use default
        default_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
        website_url = st.text_input("Website URL", value=default_url)

        # Store the URL in state manager
        if state.website_url != website_url:
            state.website_url = website_url
            logger.info(f"State change: website_url = {website_url}")

    # API Key section in collapsible expander
    with st.expander("API Configuration", expanded=False):
        # Try to get the API key from config or environment
        default_api_key = load_google_api_key()

        # Store the API key in state manager for later use
        if not hasattr(state, 'google_api_key') or not state.google_api_key:
            state.google_api_key = default_api_key

        # Only show API key input if no key was found
        if not default_api_key:
            api_key_col1, api_key_col2 = st.columns([3, 1])
            with api_key_col1:
                google_api_key = st.text_input(
                    "Google API Key",
                    type="password",
                    help="Required for AI features"
                )
                state.google_api_key = google_api_key

            with api_key_col2:
                if st.button("Save Key", help="Save to config.json"):
                    if google_api_key:
                        try:
                            config = {"google_api_key": google_api_key}
                            with open(APP_CONFIG_FILE, "w") as f:
                                json.dump(config, f, indent=2)
                            st.success("✅ API key saved")

                            # Also set as environment variable for this session
                            os.environ["GOOGLE_API_KEY"] = google_api_key
                        except Exception as e:
                            st.error(f"❌ Error: {e}")
                    else:
                        st.warning("⚠️ Enter an API key")
        else:
            # Just show a message that an API key was found
            st.success("✅ Google API key configured")
            # Store the API key in state manager
            state.google_api_key = default_api_key

    # Advanced options in collapsible expander
    with st.expander("Advanced Options", expanded=False):
        st.markdown("##### Element Detection Settings")

        # Options for element detection and matching
        col1, col2 = st.columns(2)
        with col1:
            # Note: This variable is used implicitly by Streamlit's state management
            auto_detect = st.checkbox("Auto-detect UI elements", value=True, key="auto_detect",
                                help="Automatically detect elements from website")
            # Store in state manager for consistency
            state.auto_detect = auto_detect

        with col2:
            # Get default value from state if available
            default_ai_matching = state.use_ai_matching if hasattr(state, 'use_ai_matching') else True
            use_ai_matching = st.checkbox("AI element matching", value=default_ai_matching,
                                        help="Use AI to match test steps to UI elements")
            # Store in state manager for use in other stages
            if not hasattr(state, 'use_ai_matching') or state.use_ai_matching != use_ai_matching:
                state.use_ai_matching = use_ai_matching
                logger.info(f"State change: use_ai_matching = {use_ai_matching}")

def stage3_convert_test_case(state):
    """Phase 3: Test Case Analysis and Conversion."""
    st.markdown("<h2 class='stage-header'>Phase 3: Test Case Analysis</h2>", unsafe_allow_html=True)

    # Initialize state variables for step-by-step workflow
    if not hasattr(state, 'selected_test_case') or state.selected_test_case is None:
        state.selected_test_case = None
    if not hasattr(state, 'selected_step') or state.selected_step is None:
        state.selected_step = None
    if not hasattr(state, 'step_elements') or state.step_elements is None:
        state.step_elements = []
    if not hasattr(state, 'step_matches') or state.step_matches is None:
        state.step_matches = {}

    # Test Case Selection Section
    st.markdown("#### Select Test Case")

    # Add test case selection dropdown
    selected_test_case = None
    if state.test_cases:
        # Ensure test_cases is a list
        if not isinstance(state.test_cases, list):
            st.error("❌ Invalid test case format")
            return

        test_case_options = []
        for tc in state.test_cases:
            if isinstance(tc, dict):
                tc_id = tc.get('Test Case ID', '')
                objective = tc.get('Test Case Objective', '')[:50]
                if tc_id and objective:
                    test_case_options.append(f"{tc_id} - {objective}...")

        if test_case_options:
            selected_option = st.selectbox(
                "Test Case",
                ["Select a test case..."] + test_case_options
            )

            if selected_option != "Select a test case...":
                selected_tc_id = selected_option.split(" - ")[0]

                selected_test_case = next(
                    (tc for tc in state.test_cases if tc.get('Test Case ID') == selected_tc_id),
                    None
                )

                if selected_test_case:
                    # Check if this is a different test case than the currently selected one
                    is_new_test_case = (not hasattr(state, 'selected_test_case') or
                                       not state.selected_test_case or
                                       state.selected_test_case.get('Test Case ID') != selected_test_case.get('Test Case ID'))

                    if is_new_test_case:
                        # Ask for confirmation if we already have a test case with progress
                        has_progress = (hasattr(state, 'step_table_json') and state.step_table_json and
                                       (hasattr(state, 'current_step_index') and state.current_step_index > 0))

                        if has_progress:
                            with st.expander("⚠️ Warning: Progress Will Be Reset", expanded=True):
                                st.warning("Changing test cases will reset all progress on the current test case.")
                                confirm = st.button("Confirm Change", key="confirm_test_case_change")

                                if not confirm:
                                    st.info("Select the same test case to continue with your current progress.")
                                    return

                        # Reset test case state with confirmation
                        state.reset_test_case_state(confirm=True, reason=f"New test case selected: {selected_test_case.get('Test Case ID')}")

                    # Store the original test case in state manager before conversion
                    state.selected_test_case = selected_test_case
                    state.original_test_case = selected_test_case.copy() if isinstance(selected_test_case, dict) else selected_test_case

                    # Display test case details in a collapsible section
                    with st.expander("Test Case Details", expanded=True):
                        test_case_details_col1, test_case_details_col2 = st.columns(2)
                        with test_case_details_col1:
                            st.markdown(f"**ID:** {selected_test_case.get('Test Case ID')}")
                            st.markdown(f"**Steps:** {len(selected_test_case.get('Steps', []))}")
                        with test_case_details_col2:
                            st.markdown(f"**Objective:** {selected_test_case.get('Test Case Objective')}")

                    # Check if conversion has already been done
                    if hasattr(state, 'step_table_markdown') and state.step_table_markdown and hasattr(state, 'conversion_done') and state.conversion_done:
                        st.success("✅ Test case converted to automation format")

                        # Display UI element detection recommendation in an expander
                        if hasattr(state, 'step_table_analysis') and state.step_table_analysis:
                            with st.expander("Analysis Results", expanded=False):
                                step_table_analysis = state.step_table_analysis
                                if step_table_analysis.get("requires_ui_elements", True):
                                    st.info(f"🔍 UI Element Detection Recommended: {step_table_analysis.get('reason', 'For proper automation')}")
                                else:
                                    st.success(f"✅ UI Element Detection Not Needed: {step_table_analysis.get('reason', 'No UI elements required')}")

                        # Add button to re-convert if needed
                        if st.button("Re-Convert Test Case", key="reconvert_to_step_table_btn"):
                            state.conversion_done = False
                            st.rerun()

                        # Show the converted step table in an expander
                        with st.expander("View Converted Step Table", expanded=True):
                            # Display the step table in a more interactive way
                            tab1, tab2 = st.tabs(["Markdown Format", "JSON Data"])

                            with tab1:
                                st.markdown(state.step_table_markdown)
                                # Add a button to copy the step table to clipboard
                                if st.button("Copy Markdown", key="copy_step_table_btn", help="Copy to clipboard"):
                                    try:
                                        import pyperclip
                                        pyperclip.copy(state.step_table_markdown)
                                        st.success("✅ Copied to clipboard")
                                    except ImportError:
                                        st.warning("⚠️ pyperclip module not installed")
                                    except Exception as e:
                                        st.error(f"❌ Error: {e}")

                            with tab2:
                                # Use st.json for better formatting and interaction
                                st.json(state.step_table_json)
                                # Add a button to copy the JSON to clipboard
                                if st.button("Copy JSON", key="copy_json_btn", help="Copy to clipboard"):
                                    try:
                                        import pyperclip
                                        pyperclip.copy(json.dumps(state.step_table_json, indent=2))
                                        st.success("✅ Copied to clipboard")
                                    except ImportError:
                                        st.warning("⚠️ pyperclip module not installed")
                                    except Exception as e:
                                        st.error(f"❌ Error: {e}")
                    else:
                        # Conversion section
                        st.markdown("#### Convert to Automation Format")

                        # Add button to convert test case
                        convert_button = st.button("🔄 Convert Test Case",
                                                key="convert_to_step_table_btn",
                                                help="Convert to automation-ready format")

                        if convert_button:
                            with st.spinner("Converting test case..."):
                                try:
                                    # Use the selected test case for conversion
                                    markdown_table, json_table = convert_test_case_to_step_table(
                                        selected_test_case,
                                        state.google_api_key
                                    )

                                    # Store both the markdown table and JSON table in state manager
                                    state.step_table_markdown = markdown_table
                                    state.step_table_json = json_table
                                    state.conversion_done = True

                                    # Analyze the step table to determine if UI element detection is needed
                                    step_table_analysis = analyze_step_table((markdown_table, json_table))
                                    state.step_table_analysis = step_table_analysis

                                    # Display success message
                                    st.success("✅ Conversion successful")

                                    # Show a comparison between original and converted test case
                                    with st.expander("Compare Original vs. Converted", expanded=True):
                                        comp_col1, comp_col2 = st.columns(2)
                                        with comp_col1:
                                            st.markdown("**Original Format**")
                                            original_steps = selected_test_case.get('Steps', [])
                                            for step in original_steps:
                                                st.markdown(f"**Step {step.get('Step No')}:** {step.get('Test Steps')}")
                                                st.markdown(f"*Expected:* {step.get('Expected Result')}")
                                                st.markdown("---")

                                        with comp_col2:
                                            st.markdown("**Automation Format**")
                                            st.markdown(state.step_table_markdown)

                                    # Force a rerun to show the converted view
                                    st.rerun()
                                except Exception as e:
                                    st.error(f"❌ Conversion error: {e}")

                    # Check if step table conversion is complete before proceeding
                    if not (hasattr(state, 'step_table_markdown') and state.step_table_markdown and hasattr(state, 'conversion_done') and state.conversion_done):
                        with st.expander("Next Steps", expanded=True):
                            st.info("ℹ️ Please convert the test case to continue to Phase 4")
